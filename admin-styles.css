/* Admin Panel Styles for AL-SALAMAT */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
    text-align: right;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    transition: opacity 0.3s ease;
}

.loading-overlay.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Header */
.admin-header {
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.admin-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.admin-header h1 {
    color: #333;
    font-size: 1.5rem;
    font-weight: 600;
}

.admin-header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    border-radius: 20px;
    font-size: 0.9rem;
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #28a745;
    animation: pulse 2s infinite;
}

.status-indicator.offline {
    background: #dc3545;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Main Container */
.admin-container {
    display: flex;
    max-width: 1400px;
    margin: 0 auto;
    min-height: calc(100vh - 80px);
    gap: 2rem;
    padding: 2rem;
}

/* Sidebar */
.admin-sidebar {
    width: 280px;
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    height: fit-content;
    position: sticky;
    top: 100px;
}

.admin-menu {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.menu-item {
    background: transparent;
    border: none;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    text-align: right;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    color: #666;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.menu-item:hover {
    background: #f8f9fa;
    color: #333;
    transform: translateX(-5px);
}

.menu-item.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* Main Content */
.admin-main {
    flex: 1;
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    max-height: calc(100vh - 120px);
    overflow-y: auto;
}

/* Sections */
.admin-section {
    display: none;
}

.admin-section.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.section-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f3f4;
}

.section-header h2 {
    color: #333;
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
}

.section-header p {
    color: #666;
    font-size: 1rem;
}

/* Cards */
.admin-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid #e9ecef;
}

.admin-card h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

/* Forms */
.admin-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.form-group input,
.form-group textarea,
.form-group select {
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group small {
    color: #666;
    font-size: 0.8rem;
}

/* Buttons */
.admin-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.admin-btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.admin-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.admin-btn.secondary {
    background: #6c757d;
    color: white;
}

.admin-btn.secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.admin-btn.danger {
    background: #dc3545;
    color: white;
}

.admin-btn.danger:hover {
    background: #c82333;
    transform: translateY(-2px);
}

.admin-btn.success {
    background: #28a745;
    color: white;
}

.admin-btn.success:hover {
    background: #218838;
    transform: translateY(-2px);
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

/* Messages */
.admin-message {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    display: none;
    font-weight: 500;
}

.admin-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.admin-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.admin-message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Tables */
.table-container {
    overflow-x: auto;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.admin-table th,
.admin-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.admin-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.admin-table tr:hover {
    background: #f8f9fa;
}

/* Grids */
.branches-grid,
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.branch-item,
.gallery-item {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid #e9ecef;
    transition: transform 0.3s ease;
}

.branch-item:hover,
.gallery-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.branch-item h4 {
    color: #333;
    margin-bottom: 0.5rem;
}

.branch-item p {
    color: #666;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.branch-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 1rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    justify-content: center;
    align-items: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

/* No Data */
.no-data {
    text-align: center;
    padding: 2rem;
    color: #666;
    font-style: italic;
}

/* Messages Container */
.messages-container {
    max-height: 500px;
    overflow-y: auto;
}

.message-item {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.message-sender {
    font-weight: 600;
    color: #333;
}

.message-date {
    font-size: 0.8rem;
    color: #666;
}

.message-content {
    color: #555;
    line-height: 1.5;
}

/* Gallery Management Styles */
.gallery-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    gap: 1rem;
    flex-wrap: wrap;
}

.search-box {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
    min-width: 250px;
}

.search-box input {
    flex: 1;
    padding: 0.8rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 0.9rem;
}

.search-box button {
    padding: 0.8rem 1rem;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-box button:hover {
    background: #5a6fd8;
}

.filter-controls select {
    padding: 0.8rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 0.9rem;
    min-width: 150px;
}

.gallery-images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.gallery-image-item {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.gallery-image-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.gallery-image-preview {
    width: 100%;
    height: 200px;
    object-fit: cover;
    display: block;
}

.gallery-image-info {
    padding: 1rem;
}

.gallery-image-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.gallery-image-description {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.8rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.gallery-image-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.8rem;
    color: #888;
}

.gallery-image-category {
    background: #667eea;
    color: white;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-size: 0.75rem;
}

.gallery-image-featured {
    background: #ffd700;
    color: #333;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
}

.gallery-image-actions {
    display: flex;
    gap: 0.5rem;
}

.gallery-image-actions button {
    flex: 1;
    padding: 0.6rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.edit-btn {
    background: #28a745;
    color: white;
}

.edit-btn:hover {
    background: #218838;
}

.delete-btn {
    background: #dc3545;
    color: white;
}

.delete-btn:hover {
    background: #c82333;
}

.upload-progress {
    margin-top: 1rem;
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e1e5e9;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.pagination button {
    padding: 0.6rem 1rem;
    border: 2px solid #667eea;
    background: white;
    color: #667eea;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pagination button:hover,
.pagination button.active {
    background: #667eea;
    color: white;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-container {
        flex-direction: column;
        padding: 1rem;
        gap: 1rem;
    }
    
    .admin-sidebar {
        width: 100%;
        position: static;
    }
    
    .admin-menu {
        flex-direction: row;
        overflow-x: auto;
        gap: 0.5rem;
    }
    
    .menu-item {
        white-space: nowrap;
        min-width: 150px;
    }
    
    .admin-header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .admin-header-actions {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .branches-grid,
    .gallery-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .admin-btn {
        justify-content: center;
    }

    .gallery-controls {
        flex-direction: column;
        gap: 1rem;
    }

    .search-box {
        min-width: 100%;
    }

    .gallery-images-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
    }

    .gallery-image-preview {
        height: 150px;
    }

    .pagination {
        flex-wrap: wrap;
        gap: 0.3rem;
    }

    .pagination button {
        padding: 0.5rem 0.8rem;
        font-size: 0.8rem;
    }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.3s ease;
}

.slide-up {
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Success Animation */
.success-animation {
    animation: successPulse 0.6s ease;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Badge Styles */
.badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.badge.admin {
    background: #dc3545;
    color: white;
}

.badge.user {
    background: #6c757d;
    color: white;
}

/* File Input Styling */
input[type="file"] {
    padding: 0.5rem;
    border: 2px dashed #e9ecef;
    border-radius: 8px;
    background: #f8f9fa;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

input[type="file"]:hover {
    border-color: #667eea;
}

/* Enhanced Scrollbar */
.admin-main::-webkit-scrollbar,
.messages-container::-webkit-scrollbar {
    width: 8px;
}

.admin-main::-webkit-scrollbar-track,
.messages-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.admin-main::-webkit-scrollbar-thumb,
.messages-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.admin-main::-webkit-scrollbar-thumb:hover,
.messages-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Hero Images Management Styles */
.hero-images-grid,
.service-images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.hero-image-item,
.service-image-item {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
}

.hero-image-item:hover,
.service-image-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.hero-image-preview,
.service-image-preview {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.hero-image-preview img,
.service-image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.hero-image-item:hover .hero-image-preview img,
.service-image-item:hover .service-image-preview img {
    transform: scale(1.05);
}

.hero-image-overlay,
.service-image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 1rem;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.hero-image-item:hover .hero-image-overlay,
.service-image-item:hover .service-image-overlay {
    transform: translateY(0);
}

.hero-image-overlay h4,
.service-image-overlay h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #FFD700;
}

.hero-image-overlay p,
.service-image-overlay p {
    margin: 0;
    font-size: 0.9rem;
    color: #f0f0f0;
}

.service-image-overlay .image-filename {
    display: block;
    font-size: 0.7rem;
    opacity: 0.7;
    margin-top: 0.3rem;
    font-style: italic;
}

.service-image-form {
    padding: 1rem;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.service-image-form .form-group {
    margin-bottom: 1rem;
}

.service-image-form .form-group:last-child {
    margin-bottom: 0;
}

.service-image-form label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.service-image-form input,
.service-image-form textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.service-image-form input:focus,
.service-image-form textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.service-image-form textarea {
    resize: vertical;
    min-height: 60px;
}

.hero-image-actions,
.service-image-actions {
    padding: 1rem;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
}

.hero-image-actions .admin-btn,
.service-image-actions .admin-btn {
    flex: 1;
    min-width: 120px;
    justify-content: center;
    font-size: 0.9rem;
    padding: 0.6rem 1rem;
}

.hero-images-info,
.service-images-info {
    background: #e3f2fd;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1.5rem;
    border-left: 4px solid #2196f3;
}

.hero-images-info h4,
.service-images-info h4 {
    color: #1976d2;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.hero-images-info ul,
.service-images-info ul {
    margin: 0;
    padding-right: 1.5rem;
    color: #424242;
}

.hero-images-info li,
.service-images-info li {
    margin-bottom: 0.3rem;
    font-size: 0.9rem;
}

/* Upload Progress for Hero Images */
.hero-upload-progress {
    margin-top: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
    overflow: hidden;
    height: 6px;
    display: none;
}

.hero-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

/* Responsive Design Updates */
@media (max-width: 768px) {
    .hero-images-grid,
    .service-images-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .hero-image-actions,
    .service-image-actions {
        flex-direction: column;
    }

    .hero-image-actions .admin-btn,
    .service-image-actions .admin-btn {
        width: 100%;
    }
}

/* Edit Gallery Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    overflow-y: auto;
}

.modal-overlay.active {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.3rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #666;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: #f8f9fa;
    color: #dc3545;
}

.current-image-preview,
.new-image-preview {
    text-align: center;
    padding: 1rem;
    border: 2px dashed #ddd;
    border-radius: 8px;
    background: #f8f9fa;
}

.current-image-preview img,
.new-image-preview img {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.new-image-preview {
    border-color: #28a745;
    background: #f8fff9;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
    margin-left: 0.5rem;
    transform: scale(1.2);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

@media (max-width: 768px) {
    .modal-content {
        padding: 1rem;
        max-width: 95%;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .admin-btn {
        width: 100%;
    }
}

/* Logo Management Styles */
.logo-management-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.logo-preview-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.logo-preview-container {
    width: 150px;
    height: 150px;
    border: 2px dashed #ddd;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
}

.logo-preview-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
    display: none;
}

.logo-preview-image.visible {
    display: block;
}

.logo-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
    text-align: center;
}

.logo-placeholder.hidden {
    display: none;
}

.placeholder-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.logo-placeholder p {
    font-size: 0.9rem;
    margin: 0;
}

.logo-info h4 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.logo-info p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

.logo-upload-section {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.logo-upload-section h4 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.logo-settings-info {
    background: #e3f2fd;
    padding: 1.5rem;
    border-radius: 12px;
    border-left: 4px solid #2196f3;
}

.logo-settings-info h4 {
    color: #1976d2;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.logo-settings-info ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.logo-settings-info li {
    color: #424242;
    margin-bottom: 0.5rem;
    padding-right: 1rem;
    position: relative;
    font-size: 0.9rem;
}

.logo-settings-info li::before {
    content: "•";
    color: #2196f3;
    position: absolute;
    right: 0;
    font-weight: bold;
}

@media (max-width: 768px) {
    .logo-management-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .logo-preview-container {
        width: 120px;
        height: 120px;
    }
}
